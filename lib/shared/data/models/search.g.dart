// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SearchModelImpl _$$SearchModelImplFromJson(Map<String, dynamic> json) =>
    _$SearchModelImpl(
      filters: json['filters'] == null
          ? null
          : SearchFiltersModel.fromJson(
              json['filters'] as Map<String, dynamic>),
      pagination: json['pagination'] == null
          ? null
          : SearchPaginationModel.fromJson(
              json['pagination'] as Map<String, dynamic>),
      sort: json['sort'] == null
          ? null
          : SearchSortModel.fromJson(json['sort'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SearchModelImplToJson(_$SearchModelImpl instance) =>
    <String, dynamic>{
      if (instance.filters case final value?) 'filters': value,
      if (instance.pagination case final value?) 'pagination': value,
      if (instance.sort case final value?) 'sort': value,
    };

_$SearchFiltersModelImpl _$$SearchFiltersModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchFiltersModelImpl(
      query: json['query'] as String?,
      branchId: json['branchId'] as String?,
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      documentDepartment: $enumDecodeNullable(
          _$DocumentDepartmentEnumMap, json['documentDepartment']),
      projectIds: (json['projectIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      status: $enumDecodeNullable(_$ProjectStatusEnumMap, json['status']),
      userId: json['userId'] as String?,
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      root: json['root'] as bool?,
      type: $enumDecodeNullable(_$ProductTypeEnumMap, json['type']),
      parentProductId: json['parentProductId'] as String?,
      roles: (json['roles'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$UserRoleEnumMap, e))
          .toList(),
      projectId: json['projectId'] as String?,
      productId: json['productId'] as String?,
      bluePrint: json['bluePrint'] as bool?,
      isFolder: json['isFolder'] as bool?,
      extension: json['extension'] as String?,
      parentFolderId: json['parentFolderId'] as String?,
      version: (json['version'] as num?)?.toInt(),
      materialId: json['materialId'] as String?,
      storageType:
          $enumDecodeNullable(_$StorageTypeEnumMap, json['storageType']),
      materialType:
          $enumDecodeNullable(_$NomenclatureTypeEnumMap, json['materialType']),
      baseUnit: $enumDecodeNullable(_$UnitTypeEnumMap, json['baseUnit']),
      visible: json['visible'] as bool?,
      remainingMaterials: json['remainingMaterials'] as bool?,
      workerId: json['workerId'] as String?,
      taskStatus: $enumDecodeNullable(_$TaskStatusEnumMap, json['taskStatus']),
      fromDate: json['fromDate'] == null
          ? null
          : DateTime.parse(json['fromDate'] as String),
      toDate: json['toDate'] == null
          ? null
          : DateTime.parse(json['toDate'] as String),
      clientType: $enumDecodeNullable(_$ClientTypeEnumMap, json['clientType']),
      provisionId: json['provisionId'] as String?,
      deliveryStatus: (json['deliveryStatus'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$DeliveryStatusEnumMap, e))
          .toList(),
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
      drawingNumber: json['drawingNumber'] as String?,
      drawingNumbers: (json['drawingNumbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      name: json['name'] as String?,
      names:
          (json['names'] as List<dynamic>?)?.map((e) => e as String).toList(),
      material: json['material'] as String?,
      materials: (json['materials'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      feature:
          $enumDecodeNullable(_$ParametersFeatureTypeEnumMap, json['feature']),
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ParametersFeatureTypeEnumMap, e))
          .toList(),
      massFrom: (json['massFrom'] as num?)?.toDouble(),
      massTo: (json['massTo'] as num?)?.toDouble(),
      quantityFrom: (json['quantityFrom'] as num?)?.toInt(),
      quantityTo: (json['quantityTo'] as num?)?.toInt(),
      totalMassFrom: (json['totalMassFrom'] as num?)?.toDouble(),
      totalMassTo: (json['totalMassTo'] as num?)?.toDouble(),
      requirement: json['requirement'] as String?,
      requirements: (json['requirements'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      materialRequirement: json['materialRequirement'] as String?,
      materialRequirements: (json['materialRequirements'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      priority: (json['priority'] as num?)?.toInt(),
      priorities: (json['priorities'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      responsiblePerson: json['responsiblePerson'] as String?,
      responsiblePersons: (json['responsiblePersons'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      taskDateFrom: json['taskDateFrom'] == null
          ? null
          : DateTime.parse(json['taskDateFrom'] as String),
      taskDateTo: json['taskDateTo'] == null
          ? null
          : DateTime.parse(json['taskDateTo'] as String),
      supplyStatus: (json['supplyStatus'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$SupplyStatusEnumMap, e))
          .toList(),
      plannedContractDateFrom: json['plannedContractDateFrom'] == null
          ? null
          : DateTime.parse(json['plannedContractDateFrom'] as String),
      plannedContractDateTo: json['plannedContractDateTo'] == null
          ? null
          : DateTime.parse(json['plannedContractDateTo'] as String),
      lotNumbers: (json['lotNumbers'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      lotNames: (json['lotNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      contractNumber: json['contractNumber'] as String?,
      contractNumbers: (json['contractNumbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      supplier: json['supplier'] as String?,
      suppliers: (json['suppliers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      deliveryDateFrom: json['deliveryDateFrom'] == null
          ? null
          : DateTime.parse(json['deliveryDateFrom'] as String),
      deliveryDateTo: json['deliveryDateTo'] == null
          ? null
          : DateTime.parse(json['deliveryDateTo'] as String),
      costFrom: (json['costFrom'] as num?)?.toDouble(),
      costTo: (json['costTo'] as num?)?.toDouble(),
      lotIds:
          (json['lotIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
      contractIds: (json['contractIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      parentName: json['parentName'] as String?,
      parentNames: (json['parentNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$SearchFiltersModelImplToJson(
        _$SearchFiltersModelImpl instance) =>
    <String, dynamic>{
      if (instance.query case final value?) 'query': value,
      if (instance.branchId case final value?) 'branchId': value,
      if (instance.department case final value?) 'department': value,
      if (instance.documentDepartment case final value?)
        'documentDepartment': value,
      if (instance.projectIds case final value?) 'projectIds': value,
      if (_$ProjectStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.productIds case final value?) 'productIds': value,
      if (instance.root case final value?) 'root': value,
      if (_$ProductTypeEnumMap[instance.type] case final value?) 'type': value,
      if (instance.parentProductId case final value?) 'parentProductId': value,
      if (instance.roles?.map((e) => _$UserRoleEnumMap[e]!).toList()
          case final value?)
        'roles': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.bluePrint case final value?) 'bluePrint': value,
      if (instance.isFolder case final value?) 'isFolder': value,
      if (instance.extension case final value?) 'extension': value,
      if (instance.parentFolderId case final value?) 'parentFolderId': value,
      if (instance.version case final value?) 'version': value,
      if (instance.materialId case final value?) 'materialId': value,
      if (_$StorageTypeEnumMap[instance.storageType] case final value?)
        'storageType': value,
      if (_$NomenclatureTypeEnumMap[instance.materialType] case final value?)
        'materialType': value,
      if (_$UnitTypeEnumMap[instance.baseUnit] case final value?)
        'baseUnit': value,
      if (instance.visible case final value?) 'visible': value,
      if (instance.remainingMaterials case final value?)
        'remainingMaterials': value,
      if (instance.workerId case final value?) 'workerId': value,
      if (_$TaskStatusEnumMap[instance.taskStatus] case final value?)
        'taskStatus': value,
      if (instance.fromDate?.toIso8601String() case final value?)
        'fromDate': value,
      if (instance.toDate?.toIso8601String() case final value?) 'toDate': value,
      if (_$ClientTypeEnumMap[instance.clientType] case final value?)
        'clientType': value,
      if (instance.provisionId case final value?) 'provisionId': value,
      if (instance.deliveryStatus
              ?.map((e) => _$DeliveryStatusEnumMap[e]!)
              .toList()
          case final value?)
        'deliveryStatus': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.dateFrom?.toIso8601String() case final value?)
        'dateFrom': value,
      if (instance.dateTo?.toIso8601String() case final value?) 'dateTo': value,
      if (instance.drawingNumber case final value?) 'drawingNumber': value,
      if (instance.drawingNumbers case final value?) 'drawingNumbers': value,
      if (instance.name case final value?) 'name': value,
      if (instance.names case final value?) 'names': value,
      if (instance.material case final value?) 'material': value,
      if (instance.materials case final value?) 'materials': value,
      if (_$ParametersFeatureTypeEnumMap[instance.feature] case final value?)
        'feature': value,
      if (instance.features
              ?.map((e) => _$ParametersFeatureTypeEnumMap[e]!)
              .toList()
          case final value?)
        'features': value,
      if (instance.massFrom case final value?) 'massFrom': value,
      if (instance.massTo case final value?) 'massTo': value,
      if (instance.quantityFrom case final value?) 'quantityFrom': value,
      if (instance.quantityTo case final value?) 'quantityTo': value,
      if (instance.totalMassFrom case final value?) 'totalMassFrom': value,
      if (instance.totalMassTo case final value?) 'totalMassTo': value,
      if (instance.requirement case final value?) 'requirement': value,
      if (instance.requirements case final value?) 'requirements': value,
      if (instance.materialRequirement case final value?)
        'materialRequirement': value,
      if (instance.materialRequirements case final value?)
        'materialRequirements': value,
      if (instance.priority case final value?) 'priority': value,
      if (instance.priorities case final value?) 'priorities': value,
      if (instance.responsiblePerson case final value?)
        'responsiblePerson': value,
      if (instance.responsiblePersons case final value?)
        'responsiblePersons': value,
      if (instance.taskDateFrom?.toIso8601String() case final value?)
        'taskDateFrom': value,
      if (instance.taskDateTo?.toIso8601String() case final value?)
        'taskDateTo': value,
      if (instance.supplyStatus?.map((e) => _$SupplyStatusEnumMap[e]!).toList()
          case final value?)
        'supplyStatus': value,
      if (instance.plannedContractDateFrom?.toIso8601String() case final value?)
        'plannedContractDateFrom': value,
      if (instance.plannedContractDateTo?.toIso8601String() case final value?)
        'plannedContractDateTo': value,
      if (instance.lotNumbers case final value?) 'lotNumbers': value,
      if (instance.lotNames case final value?) 'lotNames': value,
      if (instance.contractNumber case final value?) 'contractNumber': value,
      if (instance.contractNumbers case final value?) 'contractNumbers': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.suppliers case final value?) 'suppliers': value,
      if (instance.deliveryDateFrom?.toIso8601String() case final value?)
        'deliveryDateFrom': value,
      if (instance.deliveryDateTo?.toIso8601String() case final value?)
        'deliveryDateTo': value,
      if (instance.costFrom case final value?) 'costFrom': value,
      if (instance.costTo case final value?) 'costTo': value,
      if (instance.lotIds case final value?) 'lotIds': value,
      if (instance.contractIds case final value?) 'contractIds': value,
      if (instance.parentName case final value?) 'parentName': value,
      if (instance.parentNames case final value?) 'parentNames': value,
    };

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};

const _$DocumentDepartmentEnumMap = {
  DocumentDepartment.management: 'management',
  DocumentDepartment.ogs: 'ogs',
  DocumentDepartment.general: 'general',
  DocumentDepartment.ogk: 'ogk',
  DocumentDepartment.ogt: 'ogt',
  DocumentDepartment.osivk: 'osivk',
  DocumentDepartment.sh: 'sh',
  DocumentDepartment.otk: 'otk',
  DocumentDepartment.prd: 'prd',
};

const _$ProjectStatusEnumMap = {
  ProjectStatus.newProject: 'new',
  ProjectStatus.work: 'work',
  ProjectStatus.archive: 'archive',
};

const _$ProductTypeEnumMap = {
  ProductType.assembly: 'assembly',
  ProductType.standard: 'standard',
  ProductType.materials: 'materials',
  ProductType.part: 'part',
  ProductType.other: 'other',
};

const _$UserRoleEnumMap = {
  UserRole.admin: 'admin',
  UserRole.headmanager: 'headmanager',
  UserRole.manager: 'manager',
  UserRole.worker: 'worker',
};

const _$StorageTypeEnumMap = {
  StorageType.common: 'common',
  StorageType.project: 'project',
};

const _$NomenclatureTypeEnumMap = {
  NomenclatureType.materials: 'materials',
  NomenclatureType.standard: 'standard',
  NomenclatureType.other: 'other',
};

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

const _$TaskStatusEnumMap = {
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.inReview: 'in_review',
  TaskStatus.rejected: 'rejected',
  TaskStatus.canceled: 'cancelled',
};

const _$ClientTypeEnumMap = {
  ClientType.client: 'client',
  ClientType.supplier: 'supplier',
};

const _$DeliveryStatusEnumMap = {
  DeliveryStatus.pending: 'pending',
  DeliveryStatus.inProgress: 'in_progress',
  DeliveryStatus.qcPending: 'qc_pending',
  DeliveryStatus.qcRejected: 'qc_rejected',
  DeliveryStatus.qcPartRejected: 'qc_part_rejected',
  DeliveryStatus.delivered: 'delivered',
  DeliveryStatus.cancelled: 'cancelled',
};

const _$ProvisionsFilterEnumMap = {
  ProvisionsFilter.cooperation: 'cooperation',
  ProvisionsFilter.materials: 'materials',
  ProvisionsFilter.cooperationMaterials: 'cooperation_materials',
  ProvisionsFilter.assemblyMaterials: 'assembly_materials',
  ProvisionsFilter.purchased: 'purchased',
  ProvisionsFilter.assembly: 'assembly',
};

const _$ParametersFeatureTypeEnumMap = {
  ParametersFeatureType.mechanics: 'MECHANICS',
  ParametersFeatureType.bending: 'BENDING',
  ParametersFeatureType.cutting: 'CUTTING',
  ParametersFeatureType.coating: 'COATING',
  ParametersFeatureType.heatTreatment: 'HEAT_TREATMENT',
  ParametersFeatureType.rolling: 'ROLLING',
  ParametersFeatureType.welding: 'WELDING',
};

const _$SupplyStatusEnumMap = {
  SupplyStatus.notStarted: 'not_started',
  SupplyStatus.confirmed: 'confirmed',
  SupplyStatus.delivered: 'delivered',
  SupplyStatus.qcPassed: 'qc_passed',
  SupplyStatus.pendingApproval: 'pending_approval',
  SupplyStatus.completed: 'completed',
};

_$SearchPaginationModelImpl _$$SearchPaginationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchPaginationModelImpl(
      offset: (json['offset'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SearchPaginationModelImplToJson(
        _$SearchPaginationModelImpl instance) =>
    <String, dynamic>{
      if (instance.offset case final value?) 'offset': value,
      if (instance.limit case final value?) 'limit': value,
    };

_$SearchSortModelImpl _$$SearchSortModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchSortModelImpl(
      field: json['field'] as String?,
      order: json['order'] as String?,
    );

Map<String, dynamic> _$$SearchSortModelImplToJson(
        _$SearchSortModelImpl instance) =>
    <String, dynamic>{
      if (instance.field case final value?) 'field': value,
      if (instance.order case final value?) 'order': value,
    };
