// ignore_for_file: invalid_annotation_target

import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/shared/styles/colors.dart';

// import 'package:sphere/features/purchase_list/data/models/provision.dart';

part 'delivery.freezed.dart';
part 'delivery.g.dart';

@freezed
class DeliveryGroupModel with _$DeliveryGroupModel {
  @JsonSerializable(includeIfNull: false)
  const factory DeliveryGroupModel({
    DateTime? expectedDate,
    List<DeliveryMaterialModel>? materials,
  }) = _DeliveryGroupModel;

  factory DeliveryGroupModel.fromJson(Map<String, dynamic> json) =>
      _$DeliveryGroupModelFromJson(json);
}

@freezed
class DeliveryModel with _$DeliveryModel {
  @JsonSerializable(includeIfNull: false)
  const factory DeliveryModel({
    @JsonKey(name: '_id') String? id,
    String? provisionId,
    String? projectId,
    DeliveryStatus? status,
    ClientModel? supplier,
    DateTime? expectedDate,
    // List<DateTime>? deliveryDates,
    DateTime? deliveryDate,
    List<DeliveryMaterialModel>? items,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DeliveryModel;

  factory DeliveryModel.fromJson(Map<String, dynamic> json) =>
      _$DeliveryModelFromJson(json);
}

@freezed
class DeliveryMaterialModel with _$DeliveryMaterialModel {
  @JsonSerializable(includeIfNull: false)
  const factory DeliveryMaterialModel({
    String? productId,
    String? provisionItemId,
    // String? materialRequirements,
    // List<ProvisionDeliveryLinkModel>? provisionItemLinks,
    double? quantityApproved,
    double? quantity,
    double? price,
    UnitType? unitType,
    String? materialName,
  }) = _DeliveryMaterialModel;

  factory DeliveryMaterialModel.fromJson(Map<String, dynamic> json) =>
      _$DeliveryMaterialModelFromJson(json);
}

enum DeliveryStatus {
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('qc_pending')
  qcPending,
  @JsonValue('qc_rejected')
  qcRejected,
  @JsonValue('qc_part_rejected')
  qcPartRejected,
  delivered,
  cancelled;

  String getName() {
    switch (this) {
      case DeliveryStatus.pending:
        return 'Ожидание';
      case DeliveryStatus.inProgress:
        return 'В процессе';
      case DeliveryStatus.qcPending:
        return 'Контроль качества';
      case DeliveryStatus.delivered:
        return 'Доставлено';
      case DeliveryStatus.cancelled:
        return 'Отменено';
      case DeliveryStatus.qcRejected:
        return 'Отменено ОТК';
      case DeliveryStatus.qcPartRejected:
        return 'Принято частично ОТК';
    }
  }

  Color getColor() {
    switch (this) {
      case DeliveryStatus.pending:
        return AppColors.lightWarning;
      case DeliveryStatus.inProgress:
        return AppColors.lightSecondary;
      case DeliveryStatus.qcPending:
        return AppColors.lightWarning;
      case DeliveryStatus.delivered:
        return AppColors.lightSuccess;
      case DeliveryStatus.qcRejected:
        return AppColors.lightWarning;
      case DeliveryStatus.qcPartRejected:
        return AppColors.lightError;
      case DeliveryStatus.cancelled:
        return AppColors.lightError;
    }
  }
}
