// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'defect_act.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OtkDefectActsResponseImpl _$$OtkDefectActsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDefectActsResponseImpl(
      items: (json['items'] as List<dynamic>?)
              ?.map(
                  (e) => OtkDefectActModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$OtkDefectActsResponseImplToJson(
        _$OtkDefectActsResponseImpl instance) =>
    <String, dynamic>{
      'items': instance.items,
      'totalItems': instance.totalItems,
    };

_$OtkDefectActModelImpl _$$OtkDefectActModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDefectActModelImpl(
      id: json['_id'] as String?,
      temporaryStorageId: json['temporaryStorageId'] as String?,
      deliveryId: json['deliveryId'] as String?,
      defectItems: (json['defectItems'] as List<dynamic>?)
          ?.map((e) => OtkDefectItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      decision: json['decision'] == null
          ? null
          : OtkDecisionModel.fromJson(json['decision'] as Map<String, dynamic>),
      status: json['status'] as String?,
      replacementDeliveryId: json['replacementDeliveryId'] as String?,
      repairTaskId: json['repairTaskId'] as String?,
      createdBy: json['createdBy'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      updatedBy: json['updatedBy'] as String?,
      tempStorage: json['tempStorage'] == null
          ? null
          : Warehouse.fromJson(json['tempStorage'] as Map<String, dynamic>),
      delivery: json['delivery'] == null
          ? null
          : DeliveryModel.fromJson(json['delivery'] as Map<String, dynamic>),
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product'] as Map<String, dynamic>),
      assignedUser: json['assignedUser'] == null
          ? null
          : UserModel.fromJson(json['assignedUser'] as Map<String, dynamic>),
      repairWorker: json['repairWorker'] == null
          ? null
          : UserModel.fromJson(json['repairWorker'] as Map<String, dynamic>),
      canStartRepair: json['canStartRepair'] as bool?,
      isInProgress: json['isInProgress'] as bool?,
      isCompleted: json['isCompleted'] as bool?,
      canMakeDecision: json['canMakeDecision'] as bool?,
      needsRecheck: json['needsRecheck'] as bool?,
      repairDuration: (json['repairDuration'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$OtkDefectActModelImplToJson(
        _$OtkDefectActModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.temporaryStorageId case final value?)
        'temporaryStorageId': value,
      if (instance.deliveryId case final value?) 'deliveryId': value,
      if (instance.defectItems case final value?) 'defectItems': value,
      if (instance.decision case final value?) 'decision': value,
      if (instance.status case final value?) 'status': value,
      if (instance.replacementDeliveryId case final value?)
        'replacementDeliveryId': value,
      if (instance.repairTaskId case final value?) 'repairTaskId': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.updatedBy case final value?) 'updatedBy': value,
      if (instance.tempStorage case final value?) 'tempStorage': value,
      if (instance.delivery case final value?) 'delivery': value,
      if (instance.product case final value?) 'product': value,
      if (instance.assignedUser case final value?) 'assignedUser': value,
      if (instance.repairWorker case final value?) 'repairWorker': value,
      if (instance.canStartRepair case final value?) 'canStartRepair': value,
      if (instance.isInProgress case final value?) 'isInProgress': value,
      if (instance.isCompleted case final value?) 'isCompleted': value,
      if (instance.canMakeDecision case final value?) 'canMakeDecision': value,
      if (instance.needsRecheck case final value?) 'needsRecheck': value,
      if (instance.repairDuration case final value?) 'repairDuration': value,
    };

_$OtkDefectItemModelImpl _$$OtkDefectItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDefectItemModelImpl(
      productId: json['productId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      description: json['description'] as String?,
      photos:
          (json['photos'] as List<dynamic>?)?.map((e) => e as String).toList(),
      decision: json['decision'] == null
          ? null
          : OtkDecisionModel.fromJson(json['decision'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OtkDefectItemModelImplToJson(
        _$OtkDefectItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.description case final value?) 'description': value,
      if (instance.photos case final value?) 'photos': value,
      if (instance.decision case final value?) 'decision': value,
    };

_$OtkDecisionModelImpl _$$OtkDecisionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDecisionModelImpl(
      type: $enumDecodeNullable(_$OtkDecisionTypeEnumMap, json['type']),
      decidedBy: json['decidedBy'] as String?,
      decidedAt: json['decidedAt'] == null
          ? null
          : DateTime.parse(json['decidedAt'] as String),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$OtkDecisionModelImplToJson(
        _$OtkDecisionModelImpl instance) =>
    <String, dynamic>{
      if (_$OtkDecisionTypeEnumMap[instance.type] case final value?)
        'type': value,
      if (instance.decidedBy case final value?) 'decidedBy': value,
      if (instance.decidedAt?.toIso8601String() case final value?)
        'decidedAt': value,
      if (instance.comment case final value?) 'comment': value,
    };

const _$OtkDecisionTypeEnumMap = {
  OtkDecisionType.repairableInternal: 'repairable_internal',
  OtkDecisionType.repairableSupplier: 'repairable_supplier',
  OtkDecisionType.nonRepairable: 'non_repairable',
};
