import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'delivery.freezed.dart';
part 'delivery.g.dart';

@freezed
class OtkDeliveriesResponse with _$OtkDeliveriesResponse {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDeliveriesResponse({
    @Default([]) List<OtkDeliveryModel> deliveries,
    @Default(0) int total,
  }) = _OtkDeliveriesResponse;

  factory OtkDeliveriesResponse.fromJson(Map<String, dynamic> json) =>
      _$OtkDeliveriesResponseFromJson(json);
}

@freezed
class OtkDeliveryModel with _$OtkDeliveryModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDeliveryModel({
    @JsonKey(name: '_id') String? id,
    String? deliveryId,
    String? warehouseId,
    DeliveryModel? delivery,
    DateTime? receivedAt,
    String? receivedBy,
    OtkStatus? status,
    List<OtkItemModel>? items,
    String? comment,
    DateTime? checkedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    // Дополнительные поля из API
    ContractModel? contract,
    Warehouse? warehouse,
    ProjectModel? project,
  }) = _OtkDeliveryModel;

  factory OtkDeliveryModel.fromJson(Map<String, dynamic> json) =>
      _$OtkDeliveryModelFromJson(json);
}

@freezed
class OtkItemModel with _$OtkItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkItemModel({
    String? productId,
    String? materialName,
    double? quantity,
    double? quantityApproved,
    double? quantityRejected,
    String? rejectionReason,
    bool? isApproved,
    // Дополнительные поля из API
    Map<String, dynamic>? quantitiesInUnits,
    List<String>? availableUnits,
    String? baseUnit,
  }) = _OtkItemModel;

  factory OtkItemModel.fromJson(Map<String, dynamic> json) =>
      _$OtkItemModelFromJson(json);
}

enum OtkStatus {
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('pending_qc')
  pendingQc,
  @JsonValue('qc_approved')
  qcApproved,
  @JsonValue('qc_rejected')
  qcRejected,
  @JsonValue('qc_part_rejected')
  qcPartRejected;

  String getName() {
    switch (this) {
      case OtkStatus.inProgress:
        return 'В процессе';
      case OtkStatus.pendingQc:
        return 'Ожидание контроля качества';
      case OtkStatus.qcApproved:
        return 'Контроль качества пройден';
      case OtkStatus.qcRejected:
        return 'Контроль качества не пройден';
      case OtkStatus.qcPartRejected:
        return 'Контроль качества частично не пройден';
    }
  }

  Color getColor() {
    switch (this) {
      case OtkStatus.inProgress:
        return AppColors.lightSecondary;
      case OtkStatus.pendingQc:
        return AppColors.lightWarning;
      case OtkStatus.qcApproved:
        return AppColors.lightSuccess;
      case OtkStatus.qcRejected:
        return AppColors.lightError;
      case OtkStatus.qcPartRejected:
        return AppColors.lightError;
    }
  }
}
