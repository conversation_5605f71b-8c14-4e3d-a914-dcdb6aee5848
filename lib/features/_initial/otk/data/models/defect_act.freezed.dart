// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'defect_act.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OtkDefectActsResponse _$OtkDefectActsResponseFromJson(
    Map<String, dynamic> json) {
  return _OtkDefectActsResponse.fromJson(json);
}

/// @nodoc
mixin _$OtkDefectActsResponse {
  List<OtkDefectActModel> get items => throw _privateConstructorUsedError;
  int get totalItems => throw _privateConstructorUsedError;

  /// Serializes this OtkDefectActsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDefectActsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDefectActsResponseCopyWith<OtkDefectActsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDefectActsResponseCopyWith<$Res> {
  factory $OtkDefectActsResponseCopyWith(OtkDefectActsResponse value,
          $Res Function(OtkDefectActsResponse) then) =
      _$OtkDefectActsResponseCopyWithImpl<$Res, OtkDefectActsResponse>;
  @useResult
  $Res call({List<OtkDefectActModel> items, int totalItems});
}

/// @nodoc
class _$OtkDefectActsResponseCopyWithImpl<$Res,
        $Val extends OtkDefectActsResponse>
    implements $OtkDefectActsResponseCopyWith<$Res> {
  _$OtkDefectActsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDefectActsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? totalItems = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OtkDefectActModel>,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtkDefectActsResponseImplCopyWith<$Res>
    implements $OtkDefectActsResponseCopyWith<$Res> {
  factory _$$OtkDefectActsResponseImplCopyWith(
          _$OtkDefectActsResponseImpl value,
          $Res Function(_$OtkDefectActsResponseImpl) then) =
      __$$OtkDefectActsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OtkDefectActModel> items, int totalItems});
}

/// @nodoc
class __$$OtkDefectActsResponseImplCopyWithImpl<$Res>
    extends _$OtkDefectActsResponseCopyWithImpl<$Res,
        _$OtkDefectActsResponseImpl>
    implements _$$OtkDefectActsResponseImplCopyWith<$Res> {
  __$$OtkDefectActsResponseImplCopyWithImpl(_$OtkDefectActsResponseImpl _value,
      $Res Function(_$OtkDefectActsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDefectActsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? totalItems = null,
  }) {
    return _then(_$OtkDefectActsResponseImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OtkDefectActModel>,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDefectActsResponseImpl implements _OtkDefectActsResponse {
  const _$OtkDefectActsResponseImpl(
      {final List<OtkDefectActModel> items = const [], this.totalItems = 0})
      : _items = items;

  factory _$OtkDefectActsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDefectActsResponseImplFromJson(json);

  final List<OtkDefectActModel> _items;
  @override
  @JsonKey()
  List<OtkDefectActModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final int totalItems;

  @override
  String toString() {
    return 'OtkDefectActsResponse(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDefectActsResponseImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of OtkDefectActsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDefectActsResponseImplCopyWith<_$OtkDefectActsResponseImpl>
      get copyWith => __$$OtkDefectActsResponseImplCopyWithImpl<
          _$OtkDefectActsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDefectActsResponseImplToJson(
      this,
    );
  }
}

abstract class _OtkDefectActsResponse implements OtkDefectActsResponse {
  const factory _OtkDefectActsResponse(
      {final List<OtkDefectActModel> items,
      final int totalItems}) = _$OtkDefectActsResponseImpl;

  factory _OtkDefectActsResponse.fromJson(Map<String, dynamic> json) =
      _$OtkDefectActsResponseImpl.fromJson;

  @override
  List<OtkDefectActModel> get items;
  @override
  int get totalItems;

  /// Create a copy of OtkDefectActsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDefectActsResponseImplCopyWith<_$OtkDefectActsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OtkDefectActModel _$OtkDefectActModelFromJson(Map<String, dynamic> json) {
  return _OtkDefectActModel.fromJson(json);
}

/// @nodoc
mixin _$OtkDefectActModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get temporaryStorageId => throw _privateConstructorUsedError;
  String? get deliveryId => throw _privateConstructorUsedError;
  List<OtkDefectItemModel>? get defectItems =>
      throw _privateConstructorUsedError;
  OtkDecisionModel? get decision => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  String? get replacementDeliveryId => throw _privateConstructorUsedError;
  String? get repairTaskId => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;
  Warehouse? get tempStorage => throw _privateConstructorUsedError;
  DeliveryModel? get delivery => throw _privateConstructorUsedError;
  ProductModel? get product => throw _privateConstructorUsedError;
  UserModel? get assignedUser => throw _privateConstructorUsedError;
  UserModel? get repairWorker => throw _privateConstructorUsedError;
  bool? get canStartRepair => throw _privateConstructorUsedError;
  bool? get isInProgress => throw _privateConstructorUsedError;
  bool? get isCompleted => throw _privateConstructorUsedError;
  bool? get canMakeDecision => throw _privateConstructorUsedError;
  bool? get needsRecheck => throw _privateConstructorUsedError;
  double? get repairDuration => throw _privateConstructorUsedError;

  /// Serializes this OtkDefectActModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDefectActModelCopyWith<OtkDefectActModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDefectActModelCopyWith<$Res> {
  factory $OtkDefectActModelCopyWith(
          OtkDefectActModel value, $Res Function(OtkDefectActModel) then) =
      _$OtkDefectActModelCopyWithImpl<$Res, OtkDefectActModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? temporaryStorageId,
      String? deliveryId,
      List<OtkDefectItemModel>? defectItems,
      OtkDecisionModel? decision,
      String? status,
      String? replacementDeliveryId,
      String? repairTaskId,
      String? createdBy,
      DateTime? createdAt,
      DateTime? updatedAt,
      String? updatedBy,
      Warehouse? tempStorage,
      DeliveryModel? delivery,
      ProductModel? product,
      UserModel? assignedUser,
      UserModel? repairWorker,
      bool? canStartRepair,
      bool? isInProgress,
      bool? isCompleted,
      bool? canMakeDecision,
      bool? needsRecheck,
      double? repairDuration});

  $OtkDecisionModelCopyWith<$Res>? get decision;
  $WarehouseCopyWith<$Res>? get tempStorage;
  $DeliveryModelCopyWith<$Res>? get delivery;
  $ProductModelCopyWith<$Res>? get product;
}

/// @nodoc
class _$OtkDefectActModelCopyWithImpl<$Res, $Val extends OtkDefectActModel>
    implements $OtkDefectActModelCopyWith<$Res> {
  _$OtkDefectActModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? temporaryStorageId = freezed,
    Object? deliveryId = freezed,
    Object? defectItems = freezed,
    Object? decision = freezed,
    Object? status = freezed,
    Object? replacementDeliveryId = freezed,
    Object? repairTaskId = freezed,
    Object? createdBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? updatedBy = freezed,
    Object? tempStorage = freezed,
    Object? delivery = freezed,
    Object? product = freezed,
    Object? assignedUser = freezed,
    Object? repairWorker = freezed,
    Object? canStartRepair = freezed,
    Object? isInProgress = freezed,
    Object? isCompleted = freezed,
    Object? canMakeDecision = freezed,
    Object? needsRecheck = freezed,
    Object? repairDuration = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      temporaryStorageId: freezed == temporaryStorageId
          ? _value.temporaryStorageId
          : temporaryStorageId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      defectItems: freezed == defectItems
          ? _value.defectItems
          : defectItems // ignore: cast_nullable_to_non_nullable
              as List<OtkDefectItemModel>?,
      decision: freezed == decision
          ? _value.decision
          : decision // ignore: cast_nullable_to_non_nullable
              as OtkDecisionModel?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      replacementDeliveryId: freezed == replacementDeliveryId
          ? _value.replacementDeliveryId
          : replacementDeliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      repairTaskId: freezed == repairTaskId
          ? _value.repairTaskId
          : repairTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      tempStorage: freezed == tempStorage
          ? _value.tempStorage
          : tempStorage // ignore: cast_nullable_to_non_nullable
              as Warehouse?,
      delivery: freezed == delivery
          ? _value.delivery
          : delivery // ignore: cast_nullable_to_non_nullable
              as DeliveryModel?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      assignedUser: freezed == assignedUser
          ? _value.assignedUser
          : assignedUser // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      repairWorker: freezed == repairWorker
          ? _value.repairWorker
          : repairWorker // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      canStartRepair: freezed == canStartRepair
          ? _value.canStartRepair
          : canStartRepair // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInProgress: freezed == isInProgress
          ? _value.isInProgress
          : isInProgress // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      canMakeDecision: freezed == canMakeDecision
          ? _value.canMakeDecision
          : canMakeDecision // ignore: cast_nullable_to_non_nullable
              as bool?,
      needsRecheck: freezed == needsRecheck
          ? _value.needsRecheck
          : needsRecheck // ignore: cast_nullable_to_non_nullable
              as bool?,
      repairDuration: freezed == repairDuration
          ? _value.repairDuration
          : repairDuration // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OtkDecisionModelCopyWith<$Res>? get decision {
    if (_value.decision == null) {
      return null;
    }

    return $OtkDecisionModelCopyWith<$Res>(_value.decision!, (value) {
      return _then(_value.copyWith(decision: value) as $Val);
    });
  }

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WarehouseCopyWith<$Res>? get tempStorage {
    if (_value.tempStorage == null) {
      return null;
    }

    return $WarehouseCopyWith<$Res>(_value.tempStorage!, (value) {
      return _then(_value.copyWith(tempStorage: value) as $Val);
    });
  }

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeliveryModelCopyWith<$Res>? get delivery {
    if (_value.delivery == null) {
      return null;
    }

    return $DeliveryModelCopyWith<$Res>(_value.delivery!, (value) {
      return _then(_value.copyWith(delivery: value) as $Val);
    });
  }

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtkDefectActModelImplCopyWith<$Res>
    implements $OtkDefectActModelCopyWith<$Res> {
  factory _$$OtkDefectActModelImplCopyWith(_$OtkDefectActModelImpl value,
          $Res Function(_$OtkDefectActModelImpl) then) =
      __$$OtkDefectActModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? temporaryStorageId,
      String? deliveryId,
      List<OtkDefectItemModel>? defectItems,
      OtkDecisionModel? decision,
      String? status,
      String? replacementDeliveryId,
      String? repairTaskId,
      String? createdBy,
      DateTime? createdAt,
      DateTime? updatedAt,
      String? updatedBy,
      Warehouse? tempStorage,
      DeliveryModel? delivery,
      ProductModel? product,
      UserModel? assignedUser,
      UserModel? repairWorker,
      bool? canStartRepair,
      bool? isInProgress,
      bool? isCompleted,
      bool? canMakeDecision,
      bool? needsRecheck,
      double? repairDuration});

  @override
  $OtkDecisionModelCopyWith<$Res>? get decision;
  @override
  $WarehouseCopyWith<$Res>? get tempStorage;
  @override
  $DeliveryModelCopyWith<$Res>? get delivery;
  @override
  $ProductModelCopyWith<$Res>? get product;
}

/// @nodoc
class __$$OtkDefectActModelImplCopyWithImpl<$Res>
    extends _$OtkDefectActModelCopyWithImpl<$Res, _$OtkDefectActModelImpl>
    implements _$$OtkDefectActModelImplCopyWith<$Res> {
  __$$OtkDefectActModelImplCopyWithImpl(_$OtkDefectActModelImpl _value,
      $Res Function(_$OtkDefectActModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? temporaryStorageId = freezed,
    Object? deliveryId = freezed,
    Object? defectItems = freezed,
    Object? decision = freezed,
    Object? status = freezed,
    Object? replacementDeliveryId = freezed,
    Object? repairTaskId = freezed,
    Object? createdBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? updatedBy = freezed,
    Object? tempStorage = freezed,
    Object? delivery = freezed,
    Object? product = freezed,
    Object? assignedUser = freezed,
    Object? repairWorker = freezed,
    Object? canStartRepair = freezed,
    Object? isInProgress = freezed,
    Object? isCompleted = freezed,
    Object? canMakeDecision = freezed,
    Object? needsRecheck = freezed,
    Object? repairDuration = freezed,
  }) {
    return _then(_$OtkDefectActModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      temporaryStorageId: freezed == temporaryStorageId
          ? _value.temporaryStorageId
          : temporaryStorageId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      defectItems: freezed == defectItems
          ? _value._defectItems
          : defectItems // ignore: cast_nullable_to_non_nullable
              as List<OtkDefectItemModel>?,
      decision: freezed == decision
          ? _value.decision
          : decision // ignore: cast_nullable_to_non_nullable
              as OtkDecisionModel?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      replacementDeliveryId: freezed == replacementDeliveryId
          ? _value.replacementDeliveryId
          : replacementDeliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      repairTaskId: freezed == repairTaskId
          ? _value.repairTaskId
          : repairTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      tempStorage: freezed == tempStorage
          ? _value.tempStorage
          : tempStorage // ignore: cast_nullable_to_non_nullable
              as Warehouse?,
      delivery: freezed == delivery
          ? _value.delivery
          : delivery // ignore: cast_nullable_to_non_nullable
              as DeliveryModel?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      assignedUser: freezed == assignedUser
          ? _value.assignedUser
          : assignedUser // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      repairWorker: freezed == repairWorker
          ? _value.repairWorker
          : repairWorker // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      canStartRepair: freezed == canStartRepair
          ? _value.canStartRepair
          : canStartRepair // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInProgress: freezed == isInProgress
          ? _value.isInProgress
          : isInProgress // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      canMakeDecision: freezed == canMakeDecision
          ? _value.canMakeDecision
          : canMakeDecision // ignore: cast_nullable_to_non_nullable
              as bool?,
      needsRecheck: freezed == needsRecheck
          ? _value.needsRecheck
          : needsRecheck // ignore: cast_nullable_to_non_nullable
              as bool?,
      repairDuration: freezed == repairDuration
          ? _value.repairDuration
          : repairDuration // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDefectActModelImpl implements _OtkDefectActModel {
  const _$OtkDefectActModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.temporaryStorageId,
      this.deliveryId,
      final List<OtkDefectItemModel>? defectItems,
      this.decision,
      this.status,
      this.replacementDeliveryId,
      this.repairTaskId,
      this.createdBy,
      this.createdAt,
      this.updatedAt,
      this.updatedBy,
      this.tempStorage,
      this.delivery,
      this.product,
      this.assignedUser,
      this.repairWorker,
      this.canStartRepair,
      this.isInProgress,
      this.isCompleted,
      this.canMakeDecision,
      this.needsRecheck,
      this.repairDuration})
      : _defectItems = defectItems;

  factory _$OtkDefectActModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDefectActModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? temporaryStorageId;
  @override
  final String? deliveryId;
  final List<OtkDefectItemModel>? _defectItems;
  @override
  List<OtkDefectItemModel>? get defectItems {
    final value = _defectItems;
    if (value == null) return null;
    if (_defectItems is EqualUnmodifiableListView) return _defectItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final OtkDecisionModel? decision;
  @override
  final String? status;
  @override
  final String? replacementDeliveryId;
  @override
  final String? repairTaskId;
  @override
  final String? createdBy;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final String? updatedBy;
  @override
  final Warehouse? tempStorage;
  @override
  final DeliveryModel? delivery;
  @override
  final ProductModel? product;
  @override
  final UserModel? assignedUser;
  @override
  final UserModel? repairWorker;
  @override
  final bool? canStartRepair;
  @override
  final bool? isInProgress;
  @override
  final bool? isCompleted;
  @override
  final bool? canMakeDecision;
  @override
  final bool? needsRecheck;
  @override
  final double? repairDuration;

  @override
  String toString() {
    return 'OtkDefectActModel(id: $id, temporaryStorageId: $temporaryStorageId, deliveryId: $deliveryId, defectItems: $defectItems, decision: $decision, status: $status, replacementDeliveryId: $replacementDeliveryId, repairTaskId: $repairTaskId, createdBy: $createdBy, createdAt: $createdAt, updatedAt: $updatedAt, updatedBy: $updatedBy, tempStorage: $tempStorage, delivery: $delivery, product: $product, assignedUser: $assignedUser, repairWorker: $repairWorker, canStartRepair: $canStartRepair, isInProgress: $isInProgress, isCompleted: $isCompleted, canMakeDecision: $canMakeDecision, needsRecheck: $needsRecheck, repairDuration: $repairDuration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDefectActModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.temporaryStorageId, temporaryStorageId) ||
                other.temporaryStorageId == temporaryStorageId) &&
            (identical(other.deliveryId, deliveryId) ||
                other.deliveryId == deliveryId) &&
            const DeepCollectionEquality()
                .equals(other._defectItems, _defectItems) &&
            (identical(other.decision, decision) ||
                other.decision == decision) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.replacementDeliveryId, replacementDeliveryId) ||
                other.replacementDeliveryId == replacementDeliveryId) &&
            (identical(other.repairTaskId, repairTaskId) ||
                other.repairTaskId == repairTaskId) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy) &&
            (identical(other.tempStorage, tempStorage) ||
                other.tempStorage == tempStorage) &&
            (identical(other.delivery, delivery) ||
                other.delivery == delivery) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.assignedUser, assignedUser) ||
                other.assignedUser == assignedUser) &&
            (identical(other.repairWorker, repairWorker) ||
                other.repairWorker == repairWorker) &&
            (identical(other.canStartRepair, canStartRepair) ||
                other.canStartRepair == canStartRepair) &&
            (identical(other.isInProgress, isInProgress) ||
                other.isInProgress == isInProgress) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.canMakeDecision, canMakeDecision) ||
                other.canMakeDecision == canMakeDecision) &&
            (identical(other.needsRecheck, needsRecheck) ||
                other.needsRecheck == needsRecheck) &&
            (identical(other.repairDuration, repairDuration) ||
                other.repairDuration == repairDuration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        temporaryStorageId,
        deliveryId,
        const DeepCollectionEquality().hash(_defectItems),
        decision,
        status,
        replacementDeliveryId,
        repairTaskId,
        createdBy,
        createdAt,
        updatedAt,
        updatedBy,
        tempStorage,
        delivery,
        product,
        assignedUser,
        repairWorker,
        canStartRepair,
        isInProgress,
        isCompleted,
        canMakeDecision,
        needsRecheck,
        repairDuration
      ]);

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDefectActModelImplCopyWith<_$OtkDefectActModelImpl> get copyWith =>
      __$$OtkDefectActModelImplCopyWithImpl<_$OtkDefectActModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDefectActModelImplToJson(
      this,
    );
  }
}

abstract class _OtkDefectActModel implements OtkDefectActModel {
  const factory _OtkDefectActModel(
      {@JsonKey(name: '_id') final String? id,
      final String? temporaryStorageId,
      final String? deliveryId,
      final List<OtkDefectItemModel>? defectItems,
      final OtkDecisionModel? decision,
      final String? status,
      final String? replacementDeliveryId,
      final String? repairTaskId,
      final String? createdBy,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final String? updatedBy,
      final Warehouse? tempStorage,
      final DeliveryModel? delivery,
      final ProductModel? product,
      final UserModel? assignedUser,
      final UserModel? repairWorker,
      final bool? canStartRepair,
      final bool? isInProgress,
      final bool? isCompleted,
      final bool? canMakeDecision,
      final bool? needsRecheck,
      final double? repairDuration}) = _$OtkDefectActModelImpl;

  factory _OtkDefectActModel.fromJson(Map<String, dynamic> json) =
      _$OtkDefectActModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get temporaryStorageId;
  @override
  String? get deliveryId;
  @override
  List<OtkDefectItemModel>? get defectItems;
  @override
  OtkDecisionModel? get decision;
  @override
  String? get status;
  @override
  String? get replacementDeliveryId;
  @override
  String? get repairTaskId;
  @override
  String? get createdBy;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  String? get updatedBy;
  @override
  Warehouse? get tempStorage;
  @override
  DeliveryModel? get delivery;
  @override
  ProductModel? get product;
  @override
  UserModel? get assignedUser;
  @override
  UserModel? get repairWorker;
  @override
  bool? get canStartRepair;
  @override
  bool? get isInProgress;
  @override
  bool? get isCompleted;
  @override
  bool? get canMakeDecision;
  @override
  bool? get needsRecheck;
  @override
  double? get repairDuration;

  /// Create a copy of OtkDefectActModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDefectActModelImplCopyWith<_$OtkDefectActModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OtkDefectItemModel _$OtkDefectItemModelFromJson(Map<String, dynamic> json) {
  return _OtkDefectItemModel.fromJson(json);
}

/// @nodoc
mixin _$OtkDefectItemModel {
  String? get productId => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String>? get photos => throw _privateConstructorUsedError;
  OtkDecisionModel? get decision => throw _privateConstructorUsedError;

  /// Serializes this OtkDefectItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDefectItemModelCopyWith<OtkDefectItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDefectItemModelCopyWith<$Res> {
  factory $OtkDefectItemModelCopyWith(
          OtkDefectItemModel value, $Res Function(OtkDefectItemModel) then) =
      _$OtkDefectItemModelCopyWithImpl<$Res, OtkDefectItemModel>;
  @useResult
  $Res call(
      {String? productId,
      double? quantity,
      String? description,
      List<String>? photos,
      OtkDecisionModel? decision});

  $OtkDecisionModelCopyWith<$Res>? get decision;
}

/// @nodoc
class _$OtkDefectItemModelCopyWithImpl<$Res, $Val extends OtkDefectItemModel>
    implements $OtkDefectItemModelCopyWith<$Res> {
  _$OtkDefectItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? description = freezed,
    Object? photos = freezed,
    Object? decision = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      decision: freezed == decision
          ? _value.decision
          : decision // ignore: cast_nullable_to_non_nullable
              as OtkDecisionModel?,
    ) as $Val);
  }

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OtkDecisionModelCopyWith<$Res>? get decision {
    if (_value.decision == null) {
      return null;
    }

    return $OtkDecisionModelCopyWith<$Res>(_value.decision!, (value) {
      return _then(_value.copyWith(decision: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtkDefectItemModelImplCopyWith<$Res>
    implements $OtkDefectItemModelCopyWith<$Res> {
  factory _$$OtkDefectItemModelImplCopyWith(_$OtkDefectItemModelImpl value,
          $Res Function(_$OtkDefectItemModelImpl) then) =
      __$$OtkDefectItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      double? quantity,
      String? description,
      List<String>? photos,
      OtkDecisionModel? decision});

  @override
  $OtkDecisionModelCopyWith<$Res>? get decision;
}

/// @nodoc
class __$$OtkDefectItemModelImplCopyWithImpl<$Res>
    extends _$OtkDefectItemModelCopyWithImpl<$Res, _$OtkDefectItemModelImpl>
    implements _$$OtkDefectItemModelImplCopyWith<$Res> {
  __$$OtkDefectItemModelImplCopyWithImpl(_$OtkDefectItemModelImpl _value,
      $Res Function(_$OtkDefectItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? description = freezed,
    Object? photos = freezed,
    Object? decision = freezed,
  }) {
    return _then(_$OtkDefectItemModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      decision: freezed == decision
          ? _value.decision
          : decision // ignore: cast_nullable_to_non_nullable
              as OtkDecisionModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDefectItemModelImpl implements _OtkDefectItemModel {
  const _$OtkDefectItemModelImpl(
      {this.productId,
      this.quantity,
      this.description,
      final List<String>? photos,
      this.decision})
      : _photos = photos;

  factory _$OtkDefectItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDefectItemModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final double? quantity;
  @override
  final String? description;
  final List<String>? _photos;
  @override
  List<String>? get photos {
    final value = _photos;
    if (value == null) return null;
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final OtkDecisionModel? decision;

  @override
  String toString() {
    return 'OtkDefectItemModel(productId: $productId, quantity: $quantity, description: $description, photos: $photos, decision: $decision)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDefectItemModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.decision, decision) ||
                other.decision == decision));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, productId, quantity, description,
      const DeepCollectionEquality().hash(_photos), decision);

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDefectItemModelImplCopyWith<_$OtkDefectItemModelImpl> get copyWith =>
      __$$OtkDefectItemModelImplCopyWithImpl<_$OtkDefectItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDefectItemModelImplToJson(
      this,
    );
  }
}

abstract class _OtkDefectItemModel implements OtkDefectItemModel {
  const factory _OtkDefectItemModel(
      {final String? productId,
      final double? quantity,
      final String? description,
      final List<String>? photos,
      final OtkDecisionModel? decision}) = _$OtkDefectItemModelImpl;

  factory _OtkDefectItemModel.fromJson(Map<String, dynamic> json) =
      _$OtkDefectItemModelImpl.fromJson;

  @override
  String? get productId;
  @override
  double? get quantity;
  @override
  String? get description;
  @override
  List<String>? get photos;
  @override
  OtkDecisionModel? get decision;

  /// Create a copy of OtkDefectItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDefectItemModelImplCopyWith<_$OtkDefectItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OtkDecisionModel _$OtkDecisionModelFromJson(Map<String, dynamic> json) {
  return _OtkDecisionModel.fromJson(json);
}

/// @nodoc
mixin _$OtkDecisionModel {
  OtkDecisionType? get type => throw _privateConstructorUsedError;
  String? get decidedBy => throw _privateConstructorUsedError;
  DateTime? get decidedAt => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this OtkDecisionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDecisionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDecisionModelCopyWith<OtkDecisionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDecisionModelCopyWith<$Res> {
  factory $OtkDecisionModelCopyWith(
          OtkDecisionModel value, $Res Function(OtkDecisionModel) then) =
      _$OtkDecisionModelCopyWithImpl<$Res, OtkDecisionModel>;
  @useResult
  $Res call(
      {OtkDecisionType? type,
      String? decidedBy,
      DateTime? decidedAt,
      String? comment});
}

/// @nodoc
class _$OtkDecisionModelCopyWithImpl<$Res, $Val extends OtkDecisionModel>
    implements $OtkDecisionModelCopyWith<$Res> {
  _$OtkDecisionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDecisionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? decidedBy = freezed,
    Object? decidedAt = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OtkDecisionType?,
      decidedBy: freezed == decidedBy
          ? _value.decidedBy
          : decidedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      decidedAt: freezed == decidedAt
          ? _value.decidedAt
          : decidedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtkDecisionModelImplCopyWith<$Res>
    implements $OtkDecisionModelCopyWith<$Res> {
  factory _$$OtkDecisionModelImplCopyWith(_$OtkDecisionModelImpl value,
          $Res Function(_$OtkDecisionModelImpl) then) =
      __$$OtkDecisionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OtkDecisionType? type,
      String? decidedBy,
      DateTime? decidedAt,
      String? comment});
}

/// @nodoc
class __$$OtkDecisionModelImplCopyWithImpl<$Res>
    extends _$OtkDecisionModelCopyWithImpl<$Res, _$OtkDecisionModelImpl>
    implements _$$OtkDecisionModelImplCopyWith<$Res> {
  __$$OtkDecisionModelImplCopyWithImpl(_$OtkDecisionModelImpl _value,
      $Res Function(_$OtkDecisionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDecisionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? decidedBy = freezed,
    Object? decidedAt = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$OtkDecisionModelImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OtkDecisionType?,
      decidedBy: freezed == decidedBy
          ? _value.decidedBy
          : decidedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      decidedAt: freezed == decidedAt
          ? _value.decidedAt
          : decidedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDecisionModelImpl implements _OtkDecisionModel {
  const _$OtkDecisionModelImpl(
      {this.type, this.decidedBy, this.decidedAt, this.comment});

  factory _$OtkDecisionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDecisionModelImplFromJson(json);

  @override
  final OtkDecisionType? type;
  @override
  final String? decidedBy;
  @override
  final DateTime? decidedAt;
  @override
  final String? comment;

  @override
  String toString() {
    return 'OtkDecisionModel(type: $type, decidedBy: $decidedBy, decidedAt: $decidedAt, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDecisionModelImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.decidedBy, decidedBy) ||
                other.decidedBy == decidedBy) &&
            (identical(other.decidedAt, decidedAt) ||
                other.decidedAt == decidedAt) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, decidedBy, decidedAt, comment);

  /// Create a copy of OtkDecisionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDecisionModelImplCopyWith<_$OtkDecisionModelImpl> get copyWith =>
      __$$OtkDecisionModelImplCopyWithImpl<_$OtkDecisionModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDecisionModelImplToJson(
      this,
    );
  }
}

abstract class _OtkDecisionModel implements OtkDecisionModel {
  const factory _OtkDecisionModel(
      {final OtkDecisionType? type,
      final String? decidedBy,
      final DateTime? decidedAt,
      final String? comment}) = _$OtkDecisionModelImpl;

  factory _OtkDecisionModel.fromJson(Map<String, dynamic> json) =
      _$OtkDecisionModelImpl.fromJson;

  @override
  OtkDecisionType? get type;
  @override
  String? get decidedBy;
  @override
  DateTime? get decidedAt;
  @override
  String? get comment;

  /// Create a copy of OtkDecisionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDecisionModelImplCopyWith<_$OtkDecisionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
