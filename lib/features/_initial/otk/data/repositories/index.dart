import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/otk/data/models/defect_act.dart';
import 'package:sphere/features/_initial/otk/data/models/delivery.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class OtkRepository {
  static Future<Response<OtkDeliveriesResponse>?> deliveriesSearch(
    SearchModel data,
  ) async {
    final request = await API.request<OtkDeliveriesResponse>(
      url: '/quality-control/deliveries/search',
      body: data.toJson(),
      method: 'POST',
      fromJson: OtkDeliveriesResponse.fromJson,
    );

    return request;
  }

  static Future<Response<OtkDeliveryModel>?> deliveriesProcess(
    String deliveryId,
    List<OtkItemModel> items,
    List<File> files,
  ) async {
    final request = await API.request<OtkDeliveryModel>(
      url: '/quality-control/deliveries/process',
      body: {
        'deliveryId': deliveryId,
        'qcItems': items.map((e) => e.toJson()).toList(),
        'files': files.map((e) => e.path).toList(),
      },
      method: 'POST',
      fromJson: OtkDeliveryModel.fromJson,
    );

    return request;
  }

  static Future<Response<OtkDefectActsResponse>?> defectActsSearch(
    SearchModel data,
  ) async {
    final request = await API.request<OtkDefectActsResponse>(
      url: '/quality-control/defect-acts/search',
      body: data.toJson(),
      method: 'POST',
      fromJson: OtkDefectActsResponse.fromJson,
    );

    return request;
  }
}
